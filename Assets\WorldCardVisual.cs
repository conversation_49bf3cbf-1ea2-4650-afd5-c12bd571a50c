using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Handles the visual representation of a world card in a player's play area
/// </summary>
public class WorldCardVisual : MonoBehaviour
{
    // Reference to the celestial body this card represents
    private GameObject celestialBody;

    // Resource counts display
    private Dictionary<ResourceType, int> resourceCounts = new Dictionary<ResourceType, int>();

    private void Awake()
    {

    }

    /// <summary>
    /// Set up this card to represent a celestial body
    /// </summary>
    public void SetupCard(GameObject body)
    {
        celestialBody = body;
    }

    /// <summary>
    /// Get the celestial body this card represents
    /// </summary>
    public GameObject GetCelestialBody()
    {
        return celestialBody;
    }

    /// <summary>
    /// Handle when the mouse is released on the card
    /// </summary>
    public void OnMouseUp()
    {
        // Check if mouse is over specific UI elements
        if (IsMouseOverSpecificUI())
        {
            return;
        }

        // Check if this click should be suppressed for build modes
        if (AssemblerHandler.ShouldSuppressCardClick() || TechBuildHandler.ShouldSuppressCardClick())
        {
            Debug.Log("World card click suppressed for build mode");
            return;
        }

        // Show card details in the detail display panel
        if (CardDetailDisplay.Instance != null && celestialBody != null)
        {
            ShowWorldCardDetail(celestialBody, gameObject);
        }
    }

    private bool IsMouseOverSpecificUI()
    {
        Vector2 mousePos = Input.mousePosition;

        // Check detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            GameObject detailPanel = detailDisplay.GetDetailPanel();
            if (detailPanel != null && detailPanel.activeInHierarchy)
            {
                RectTransform panelRect = detailPanel.GetComponent<RectTransform>();
                if (panelRect != null && RectTransformUtility.RectangleContainsScreenPoint(panelRect, mousePos))
                {
                    Debug.Log("Mouse is over detail panel");
                    return true;
                }
            }
        }

        // Check log panel
        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            // Use reflection to access private field
            System.Reflection.FieldInfo logPanelField = typeof(GameLogManager).GetField("logPanelTransform",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (logPanelField != null)
            {
                RectTransform logPanelRect = logPanelField.GetValue(logManager) as RectTransform;
                if (logPanelRect != null && RectTransformUtility.RectangleContainsScreenPoint(logPanelRect, mousePos))
                {
                    Debug.Log("Mouse is over log panel");
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Static utility method to show a world card detail for any celestial body or orbital location
    /// </summary>
    public static void ShowWorldCardDetail(GameObject location, GameObject sourceObject)
    {
        if (location == null || CardDetailDisplay.Instance == null)
            return;

        // Check if this is an orbital location
        OrbitLocation orbitLocation = location.GetComponent<OrbitLocation>();
        if (orbitLocation != null)
        {
            ShowOrbitLocationDetail(orbitLocation, location, sourceObject);
            return;
        }

        // Check if this is a celestial body
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            ShowCelestialBodyDetail(planetBody, location, sourceObject);
            return;
        }

        Debug.LogWarning($"Location {location.name} has neither PlanetBody nor OrbitLocation component!");
    }

    /// <summary>
    /// Show detail panel for a celestial body
    /// </summary>
    private static void ShowCelestialBodyDetail(PlanetBody planetBody, GameObject location, GameObject sourceObject)
    {
        // Get the planet name
        string planetName = planetBody.Name;
        if (string.IsNullOrEmpty(planetName))
        {
            planetName = location.name;
        }

        // Create a basic CardData with planet info
        CardData planetData = new CardData
        {
            Name = planetName,
            Type = "World",
            SubType = planetBody.Type.ToString(),
            Tier = 0,
            Effect = CreatePlanetDescription(planetBody),
            // Add a fake SolEncyclopedia section for flavor text
            SolEncyclopedia = $"Sol Encyclopedia: {planetName} is a place in the Solar System."
        };

        // Show card in detail display
        CardDetailDisplay.Instance.ShowCard(planetData, -1, CardDetailDisplay.CardSource.WorldCard, sourceObject);
    }

    /// <summary>
    /// Show detail panel for an orbital location
    /// </summary>
    private static void ShowOrbitLocationDetail(OrbitLocation orbitComponent, GameObject location, GameObject sourceObject)
    {
        // Get the orbit name
        string orbitName = orbitComponent.Name;
        if (string.IsNullOrEmpty(orbitName))
        {
            orbitName = location.name;
        }

        // Format the orbit name to add spaces
        string formattedOrbitName = FormatOrbitLocationName(orbitName);

        // Create a basic CardData with orbit info
        CardData orbitData = new CardData
        {
            Name = formattedOrbitName,
            Type = "Orbital Location",
            SubType = "Low Orbit",
            Tier = 0,
            Effect = CreateOrbitDescription(orbitComponent),
            // Add a fake SolEncyclopedia section for flavor text
            SolEncyclopedia = $"Sol Encyclopedia: {formattedOrbitName} is an orbital location in the Solar System."
        };

        // Show card in detail display with OrbitLocation source
        CardDetailDisplay.Instance.ShowCard(orbitData, -1, CardDetailDisplay.CardSource.OrbitLocation, sourceObject);
    }

    /// <summary>
    /// Create a description of the planet for the card
    /// </summary>
    private static string CreatePlanetDescription(PlanetBody planetBody)
    {
        string desc = $"{planetBody.Name} - {planetBody.Type}";

        if (planetBody.CanAerobrake)
        {
            desc += "\nAerobraking available";
        }

        // Add deposit information
        List<ResourceDeposit> deposits = planetBody.GetDeposits();
        if (deposits.Count > 0)
        {
            desc += "";
            Dictionary<ResourceType, int> depositCounts = new Dictionary<ResourceType, int>();

            foreach (ResourceDeposit deposit in deposits)
            {
                if (!depositCounts.ContainsKey(deposit.ResourceType))
                {
                    depositCounts[deposit.ResourceType] = 0;
                }
                depositCounts[deposit.ResourceType]++;
            }

            foreach (var kvp in depositCounts)
            {
                desc += $"\n- {kvp.Key}: {kvp.Value}";
            }
        }

        return desc;
    }

    /// <summary>
    /// Create a description for the orbital location
    /// </summary>
    private static string CreateOrbitDescription(OrbitLocation orbitLocation)
    {
        string description = $"An orbital location";

        if (orbitLocation.ParentCelestialBody != null)
        {
            description += $" around {orbitLocation.ParentCelestialBody.name}";
        }

        description += ". Ships, modules, facilities, and wonders can be placed here.";

        return description;
    }

    /// <summary>
    /// Format orbital location name to add spaces (e.g., "LowEarthOrbit" -> "Low Earth Orbit")
    /// </summary>
    private static string FormatOrbitLocationName(string orbitName)
    {
        if (string.IsNullOrEmpty(orbitName))
            return orbitName;

        // Handle the common pattern: "Low[Planet]Orbit"
        if (orbitName.StartsWith("Low") && orbitName.EndsWith("Orbit"))
        {
            // Extract the planet name from between "Low" and "Orbit"
            string planetName = orbitName.Substring(3, orbitName.Length - 8); // Remove "Low" and "Orbit"
            return $"Low {planetName} Orbit";
        }

        // For other cases, just return the original name
        return orbitName;
    }

    /// <summary>
    /// Get the planet body information including aerobraking capability
    /// </summary>
    public bool CanAerobrake()
    {
        if (celestialBody == null) return false;

        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            return planetBody.CanAerobrake;
        }
        return false;
    }
}